@{
    ViewData["Title"] = "Tổng hợp đơn hàng";
}

@section Styles {
    <link href="~/css/kendo-grid-common.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/toolbar-common.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/grid-mobile.css" rel="stylesheet" asp-append-version="true" />
    <style>

        #childPAsGrid {
            overflow-x: auto !important;
        }

        #editableProductVendorGrid{
            overflow-x: auto !important;
        }

        #previewChildPAsGrid {
            overflow-x: auto !important;
        }
    </style>
}

<div>
    <h4 class="demo-section wide title">@ViewData["Title"]</h4>
    <div id="divContent">
        <div id="gridId">
        </div>
    </div>
    <div id="window"></div>
    <div id="previewWindow"></div>
    <div id="dialog"></div>
    <div id="notification"></div>
</div>

<script type="text/x-kendo-template" id="paDetailTemplate">
    <div class="pa-detail-container">
        <h6><strong>Chi tiết hợp đồng: #=code#</strong></h6>
        <div class="pa-info-row">
            <div class="pa-info-col">
                <p><strong>Nhà cung cấp:</strong> #=vendorName#</p>
                <p><strong>Tổng tiền:</strong> #=kendo.toString(totalPrice, 'n0')# VNĐ</p>
            </div>
            <div class="pa-info-col">
                <p><strong>Trạng thái:</strong>
                    #
                    var statusClass = "";
                    switch (status) {
                        case "New":
                        case "0":
                            statusClass = "new"; break;
                        case "SendVendor":
                        case "1":
                            statusClass = "sendvendor"; break;
                        case "EmailFailed":
                        case "2":
                            statusClass = "emailfailed"; break;
                        case "Cancel":
                        case "3":
                            statusClass = "cancel"; break;
                        case "Completed":
                        case "4":
                            statusClass = "completed"; break;
                        default: statusClass = "new";
                    }
                    #
                    <span class="status-badge #=statusClass#">#=statusName#</span>
                </p>
                <p><strong>Ghi chú:</strong> #=note || ''#</p>
            </div>
        </div>
        <div class="pa-items-detail">
            <h6><strong>Danh sách sản phẩm:</strong></h6>
            <div class="pa-items-grid" data-pa-id="#=id#"></div>
        </div>
    </div>
</script>

<script type="text/javascript">
    let gridId = "#gridId";
    let record = 0;

    // Set default date range to last 1 week
    var today = EndDay();
    var oneWeekAgo = new Date();
    oneWeekAgo.setDate(today.getDate() - 7);

    function viewPAGroupDetail(groupCode) {
        ajax("GET", "/PurchaseAgreement/GetPAByGroupCode", { groupCode: groupCode }, function (response) {
            showPAGroupDetail(response.data);
        }, null, false);
    }

    function showPAGroupDetail(data) {
        let myWindow = $("#window");

        let detailHtml = '<div class="pa-group-detail">' +
            '<div class="pa-info-row">' +
            '<div class="pa-info-col">' +
            '<p><strong>Mã nhóm:</strong> ' + (data.groupCode || '') + '</p>' +
            '<p><strong>Số nhà cung cấp:</strong> ' + (data.vendorCount || 0) + '</p>' +
            '<p><strong>Số sản phẩm:</strong> ' + (data.totalItemCount || 0) + '</p>' +
            '</div>' +
            '<div class="pa-info-col">' +
            '<p><strong>Tổng tiền:</strong> ' + kendo.toString(data.totalPrice || 0, "n0") + ' VNĐ</p>' +
            '<p><strong>Trạng thái:</strong> ' + (data.statusName || '') + '</p>' +
            '<p><strong>Ngày tạo:</strong> ' + (data.createdDate ? kendo.toString(kendo.parseDate(data.createdDate), "dd/MM/yyyy HH:mm") : '') + '</p>' +
            '</div>' +
            '</div>' +
            '<hr>' +
            '<h5>Danh sách hợp đồng theo nhà cung cấp:</h5>' +
            '<div id="childPAsGrid"></div>' +
            '</div>';

        myWindow.html(detailHtml);

        $("#childPAsGrid").kendoGrid({
            dataSource: {
                data: data.childPAs || [],
                schema: {
                    model: {
                        fields: {
                            totalPrice: { type: "number" }
                        }
                    }
                }
            },
            columns: [
                { field: "code", title: "Mã hợp đồng", width: "120px" },
                { field: "vendorName", title: "Nhà cung cấp", width: "200px" },
                { field: "totalPrice", title: "Tổng tiền", width: "120px", template: "#= kendo.toString(totalPrice, 'n0') # VNĐ" },
                {
                    field: "statusName",
                    title: "Trạng thái",
                    width: "120px",
                    template: function (dataItem) {
                        if (!dataItem) {
                            return '<span class="badge badge-secondary">N/A</span>';
                        }

                        let statusClass = "";
                        switch (dataItem.status) {
                            case "New":
                            case "0":
                                statusClass = "new"; break;
                            case "SendVendor":
                            case "1":
                                statusClass = "sendvendor"; break;
                            case "EmailFailed":
                            case "2":
                                statusClass = "emailfailed"; break;
                            case "PartialEmailSent":
                            case "3":
                                statusClass = "partialemailsent"; break;
                            case "Cancel":
                            case "4":
                                statusClass = "cancel"; break;
                            case "Completed":
                            case "5":
                                statusClass = "completed"; break;
                            default: statusClass = "new";
                        }
                        return `<span class="status-badge ${statusClass}">${dataItem.statusName || 'N/A'}</span>`;
                    }
                },
                {
                    title: "Số sản phẩm",
                    width: "100px",
                    template: "#= (purchaseAgreementItems && purchaseAgreementItems.length) || 0 #"
                },
                {
                    title: "Thao tác",
                    width: "200px",
                    template: function (dataItem) {
                        var resendEmailButton = '';

                        // Show resend email button for PA with status "EmailFailed" (2) or "SendVendor" (1)
                        if (dataItem.status === "EmailFailed" || dataItem.status === "2" || dataItem.status === 2 ||
                            dataItem.status === "SendVendor" || dataItem.status === "1" || dataItem.status === 1) {
                            resendEmailButton = '<button onclick="resendSinglePAEmail(\'' + dataItem.code + '\')" title="Gửi lại Email" class="btn-action btn-resend-email _permission_" data-enum="44">' +
                                '<i class="fas fa-envelope"></i>' +
                                '</button>';
                        }

                        // Export buttons - always available for all PAs
                        var exportButtons = '<button onclick="exportPAPdf(' + dataItem.id + ')" title="Xuất PDF" class="btn-action btn-export-pdf">' +
                            '<i class="fas fa-file-pdf"></i>' +
                            '</button>' +
                            '<button onclick="exportPAExcel(' + dataItem.id + ')" title="Xuất Excel" class="btn-action btn-export-excel">' +
                            '<i class="fas fa-file-excel"></i>' +
                            '</button>';

                        return '<div class="action-buttons">' + resendEmailButton + exportButtons + '</div>';
                    }
                }
            ],
            detailTemplate: kendo.template($("#paDetailTemplate").html()),
            detailInit: detailInit,
            pageable: false,
            scrollable: true,
            height: 500,
            dataBound: function (e) {
                loadMobile("#childPAsGrid");
            },
        });

        function remove() {
            setTimeout(function () {
                if ($(".k-window #window").length > 0) {
                    $("#window").parent().remove();
                    $(gridId).after("<div id='window'></div>");
                }
            }, 200);
        }

        myWindow.kendoWindow({
            width: "1400px",
            height: "800px",
            title: "CHI TIẾT TỔNG HỢP ĐƠN",
            visible: false,
            actions: ["Close"],
            resizable: true,
            draggable: true,
            modal: true,
            close: function (e) {
                remove();
            },
        }).data("kendoWindow").center();

        myWindow.data("kendoWindow").open();
    }

    function resendSinglePAEmail(paCode) {
        $('#dialog').kendoConfirm({
            title: "XÁC NHẬN GỬI LẠI EMAIL",
            content: "Bạn có chắc chắn muốn gửi lại email cho PA '" + paCode + "' không?<br/><br/>Hệ thống sẽ gửi lại email thông báo tới nhà cung cấp có địa chỉ email.",
            size: "medium",
            messages: {
                okText: "Đồng ý",
                cancel: "Hủy"
            },
        }).data("kendoConfirm").open().result.done(function () {
            ajax("POST", "/PurchaseAgreement/ResendSinglePAEmail?paCode=" + encodeURIComponent(paCode), {}, function (response) {
                if (response.isSuccess) {
                    showSuccessMessages(response.successMessageList || ["Gửi lại email thành công!"]);
                } else {
                    // Show error messages from backend (includes email failure details)
                    showErrorMessages(response.errorMessageList || response.messageList || ["Có lỗi xảy ra khi gửi lại email"]);
                }

                // Always refresh the main grid to reflect status changes (even if emails failed)
                $(gridId).data("kendoGrid").dataSource.filter({});

                // Always refresh the detail modal if it's open
                if ($("#window").data("kendoWindow") && $("#window").data("kendoWindow").element.is(":visible")) {
                    // Get the current group code from the modal and refresh it
                    var currentGroupCode = $("#childPAsGrid").closest(".pa-group-detail").find("p:contains('Mã nhóm:')").text().replace("Mã nhóm:", "").trim();
                    if (currentGroupCode) {
                        setTimeout(function() {
                            viewPAGroupDetail(currentGroupCode);
                        }, 500);
                    }
                }
            }, null, false);
        });

        $("#window").after("<div id='dialog'></div>");
    }

    // PA Export Functions
    function exportPAPdf(paId) {
        window.open(`/PurchaseAgreement/ExportPAPdf?purchaseAgreementId=${paId}`, '_blank');
    }

    function exportPAExcel(paId) {
        window.open(`/PurchaseAgreement/ExportPAExcel?purchaseAgreementId=${paId}`, '_blank');
    }



    // PAGroup Export Functions - Multiple files per group
    function exportPAGroupPdf(groupCode) {
        window.open(`/PurchaseAgreement/ExportPAGroupPdf?groupCode=${encodeURIComponent(groupCode)}`, '_blank');
    }

    function exportPAGroupExcel(groupCode) {
        window.open(`/PurchaseAgreement/ExportPAGroupExcel?groupCode=${encodeURIComponent(groupCode)}`, '_blank');
    }



    function sendToVendors(groupCode) {
        $('#dialog').kendoConfirm({
            title: "XÁC NHẬN GỬI PA TỔNG HỢP CHO NHÀ CUNG CẤP",
            content: "Bạn có chắc chắn muốn gửi PA tổng hợp '" + groupCode + "' cho nhà cung cấp không?<br/><br/>Hệ thống sẽ:<br/>• Chuyển trạng thái thành 'Đã gửi NCC'<br/>• Tự động gửi email thông báo tới nhà cung cấp",
            size: "medium",
            messages: {
                okText: "Đồng ý",
                cancel: "Hủy"
            },
        }).data("kendoConfirm").open().result.done(function () {
            ajax("PUT", "/PurchaseAgreement/SendToVendor?groupCode=" + encodeURIComponent(groupCode), {}, function (response) {
                if (response.isSuccess) {
                    showSuccessMessages(response.successMessageList || ["Gửi PA tổng hợp cho nhà cung cấp thành công!"]);
                } else {
                    // Show error messages from backend (includes email failure details)
                    showErrorMessages(response.errorMessageList || response.messageList || ["Có lỗi xảy ra khi gửi PA tổng hợp cho nhà cung cấp"]);
                }
                // Always reload grid to reflect status changes (even if emails failed)
                $(gridId).data("kendoGrid").dataSource.filter({});
            }, null, false);
        });

        $("#window").after("<div id='dialog'></div>");
    }





    function completePAGroup(groupCode) {
        $('#dialog').kendoConfirm({
            title: "XÁC NHẬN HOÀN THÀNH PA TỔNG HỢP",
            content: "Bạn có chắc chắn muốn hoàn thành PA tổng hợp '" + groupCode + "' không?<br/><br/>Sau khi hoàn thành, trạng thái sẽ được chuyển thành 'Hoàn thành' và không thể thay đổi.",
            size: "medium",
            messages: {
                okText: "Đồng ý",
                cancel: "Hủy"
            },
        }).data("kendoConfirm").open().result.done(function () {
            ajax("PUT", "/PurchaseAgreement/CompletePAGroup?groupCode=" + encodeURIComponent(groupCode), {}, function (response) {
                if (response.isSuccess) {
                    showSuccessMessages(["Hoàn thành PA tổng hợp thành công!"]);
                    $(gridId).data("kendoGrid").dataSource.filter({});
                } else {
                    showErrorMessages(response.errorMessageList || ["Có lỗi xảy ra khi hoàn thành PA tổng hợp"]);
                }
            }, null, false);
        });

        $("#window").after("<div id='dialog'></div>");
    }

    function cancelPAGroup(groupCode) {
        $('#dialog').kendoConfirm({
            title: "XÁC NHẬN HỦY PA TỔNG HỢP",
            content: "Bạn có chắc chắn muốn hủy PA tổng hợp '" + groupCode + "' không?<br/><br/>Sau khi hủy, các đơn hàng PO liên quan sẽ được khôi phục về trạng thái 'Đã xác nhận' và có thể chỉnh sửa lại.",
            size: "medium",
            messages: {
                okText: "Đồng ý",
                cancel: "Hủy"
            },
        }).data("kendoConfirm").open().result.done(function () {
            ajax("PUT", "/PurchaseAgreement/CancelPAGroup?groupCode=" + encodeURIComponent(groupCode), {}, function (response) {
                if (response.isSuccess) {
                    showSuccessMessages(response.successMessageList || ["Hủy PA tổng hợp thành công!"]);
                    $(gridId).data("kendoGrid").dataSource.filter({});
                } else {
                    showErrorMessages(response.errorMessageList || ["Có lỗi xảy ra khi hủy PA tổng hợp"]);
                }
            }, null, false);
        });

        $("#window").after("<div id='dialog'></div>");
    }

    function detailInit(e) {
        var detailRow = e.detailRow;
        var paData = e.data;

        detailRow.find(".pa-items-grid").kendoGrid({
            dataSource: {
                data: paData.purchaseAgreementItems || [],
                schema: {
                    model: {
                        fields: {
                            quantity: { type: "number" },
                            price: { type: "number" }
                        }
                    }
                }
            },
            columns: [
                { field: "productName", title: "Sản phẩm", width: "200px" },
                { field: "quantity", title: "Số lượng", width: "100px", template: "#= kendo.toString(quantity, 'n2') #", attributes: { style: "text-align: center;" } },
                { field: "unitName", title: "Đơn vị", width: "80px", attributes: { style: "text-align: center;" } },
                { field: "price", title: "Đơn giá", width: "120px", template: "#= kendo.toString(price, 'n0') # VNĐ", attributes: { style: "text-align: right;" } },
                {
                    title: "Thành tiền",
                    width: "120px",
                    template: "#= kendo.toString(parseFloat(quantity || 0) * parseFloat(price || 0), 'n0') # VNĐ",
                    attributes: { style: "text-align: right;" }
                },
                { field: "note", title: "Ghi chú", width: "150px" }
            ],
            pageable: false,
            scrollable: true,
            height: 200
        });
    }

    function showPAGroupPreview() {
        ajax("GET", "/PurchaseAgreement/GetPAGroupPreview", {}, function (response) {
            if (response.isSuccess && response.data) {
                showPAGroupPreviewModal(response.data);
            } else {
                // Check if the error is about no confirmed purchase orders
                const errorMessages = response.errorMessageList || ["Không thể lấy dữ liệu preview"];
                const hasNoConfirmedOrdersError = errorMessages.some(msg =>
                    msg.includes("Không có đơn hàng nào ở trạng thái Đã xác nhận"));

                if (hasNoConfirmedOrdersError) {
                    // Show a more helpful error message with guidance and a link
                    const errorHtml = `
                        <div>
                            <p><strong>Không có đơn hàng nào ở trạng thái 'Đã xác nhận' để tạo PA tổng hợp.</strong></p>
                            <p>Vui lòng:</p>
                            <ol>
                                <li><a href="/PurchaseOrder/Index" target="_blank" style="color: #007bff; text-decoration: underline;">Vào trang Đơn hàng (Purchase Order)</a></li>
                                <li>Xác nhận các đơn hàng cần thiết</li>
                                <li>Quay lại đây để tạo PA tổng hợp</li>
                            </ol>
                        </div>
                    `;

                    // Use kendo notification to show HTML content
                    var notification = $("#notification").kendoNotification({
                        position: {
                            pinned: true,
                            top: 30,
                            right: 30
                        },
                        autoHideAfter: 10000,
                        stacking: "down",
                        templates: [{
                            type: "error",
                            template: "<div class='error-notification'>#= message #</div>"
                        }]
                    }).data("kendoNotification");

                    notification.show({
                        message: errorHtml
                    }, "error");
                } else {
                    showErrorMessages(errorMessages);
                }
            }
        }, null, false);
    }

    function showEditablePAGroupPreview() {
        ajax("GET", "/PurchaseAgreement/GetEditablePAGroupPreview", {}, function (response) {
            if (response.isSuccess && response.data) {
                showEditablePAGroupPreviewModal(response.data);
            } else {

                // Check if the error is about no confirmed purchase orders
                const errorMessages = response.errorMessageList || ["Không thể lấy dữ liệu preview có thể chỉnh sửa"];
                const hasNoConfirmedOrdersError = errorMessages.some(msg =>
                    msg.includes("Không có đơn hàng nào ở trạng thái Đã xác nhận"));

                if (hasNoConfirmedOrdersError) {
                    // Show a more helpful error message with guidance and a link
                    const errorHtml = `
                        <div>
                            <p><strong>Không có đơn hàng nào ở trạng thái 'Đã xác nhận' để tạo PA tổng hợp.</strong></p>
                            <p>Vui lòng:</p>
                            <ol>
                                <li><a href="/PurchaseOrder/Index" target="_blank" style="color: #007bff; text-decoration: underline;">Vào trang Đơn hàng (Purchase Order)</a></li>
                                <li>Xác nhận các đơn hàng cần thiết</li>
                                <li>Quay lại đây để tạo PA tổng hợp</li>
                            </ol>
                        </div>
                    `;

                    // Use kendo notification to show HTML content
                    var notification = $("#notification").kendoNotification({
                        position: {
                            pinned: true,
                            top: 30,
                            right: 30
                        },
                        autoHideAfter: 10000,
                        stacking: "down",
                        templates: [{
                            type: "error",
                            template: "<div class='error-notification'>#= message #</div>"
                        }]
                    }).data("kendoNotification");

                    notification.show({
                        message: errorHtml
                    }, "error");
                } else {
                    showErrorMessages(errorMessages);
                }
            }
        }, null, false);
    }

    function showPAGroupPreviewModal(data) {
        let previewWindow = $("#previewWindow");
        let previewHtml = '<div class="pa-group-preview">' +
            '<div class="preview-header">' +
            '<h5 style="color: #007bff; margin-bottom: 15px;">PREVIEW - TỔNG HỢP ĐƠN SẼ ĐƯỢC TẠO</h5>' +
            '</div>' +
            '<div class="pa-info-row">' +
            '<div class="pa-info-col">' +
            '<p><strong>Mã nhóm:</strong> ' + (data.groupCode || '') + '</p>' +
            '<p><strong>Số nhà cung cấp:</strong> ' + (data.vendorCount || 0) + '</p>' +
            '<p><strong>Số sản phẩm:</strong> ' + (data.totalItemCount || 0) + '</p>' +
            '</div>' +
            '<div class="pa-info-col">' +
            '<p><strong>Tổng tiền:</strong> ' + kendo.toString(data.totalPrice || 0, "n0") + ' VNĐ</p>' +
            '<p><strong>Trạng thái:</strong> ' + (data.statusName || '') + '</p>' +
            '<p><strong>Ngày tạo:</strong> ' + (data.createdDate ? kendo.toString(kendo.parseDate(data.createdDate), "dd/MM/yyyy HH:mm") : '') + '</p>' +
            '</div>' +
            '</div>' +
            '<hr>' +
            '<h5>Danh sách hợp đồng theo nhà cung cấp (Preview):</h5>' +
            '<div id="previewChildPAsGrid"></div>' +
            '<div class="preview-actions" style="margin-top: 20px; text-align: center;">' +
            '<button id="confirmCreatePA" class="k-button k-button-md k-rounded-md k-button-solid k-button-solid-success" style="margin-right: 10px;">' +
            '<span class="k-icon k-i-check k-button-icon"></span>' +
            '<span class="k-button-text">Xác nhận tạo</span>' +
            '</button>' +
            '<button id="cancelPreview" class="k-button k-button-md k-rounded-md k-button-solid k-button-solid-base">' +
            '<span class="k-icon k-i-cancel k-button-icon"></span>' +
            '<span class="k-button-text">Hủy</span>' +
            '</button>' +
            '</div>' +
            '</div>';

        previewWindow.html(previewHtml);

        $("#previewChildPAsGrid").kendoGrid({
            dataSource: {
                data: data.childPAs || [],
                schema: {
                    model: {
                        fields: {
                            totalPrice: { type: "number" }
                        }
                    }
                }
            },
            columns: [
                { field: "code", title: "Mã hợp đồng", width: "120px" },
                { field: "vendorName", title: "Nhà cung cấp", width: "200px" },
                { field: "totalPrice", title: "Tổng tiền", width: "120px", template: "#= kendo.toString(totalPrice, 'n0') # VNĐ" },
                {
                    field: "statusName",
                    title: "Trạng thái",
                    width: "120px",
                    template: function (dataItem) {
                        if (!dataItem) {
                            return '<span class="badge badge-secondary">N/A</span>';
                        }

                        let statusClass = "";
                        switch (dataItem.status) {
                            case "New":
                            case "0":
                                statusClass = "new"; break;
                            case "SendVendor":
                            case "1":
                                statusClass = "sendvendor"; break;
                            case "EmailFailed":
                            case "2":
                                statusClass = "emailfailed"; break;
                            case "PartialEmailSent":
                            case "3":
                                statusClass = "partialemailsent"; break;
                            case "Cancel":
                            case "4":
                                statusClass = "cancel"; break;
                            case "Completed":
                            case "5":
                                statusClass = "completed"; break;
                            default: statusClass = "new";
                        }
                        return `<span class="status-badge ${statusClass}">${dataItem.statusName || 'N/A'}</span>`;
                    }
                },
                {
                    title: "Số sản phẩm",
                    width: "100px",
                    template: "#= (purchaseAgreementItems && purchaseAgreementItems.length) || 0 #"
                }
            ],
            detailTemplate: kendo.template($("#paDetailTemplate").html()),
            detailInit: detailInit,
            pageable: false,
            scrollable: true,
            height: 400,
            dataBound: function (e) {
                loadMobile("#previewChildPAsGrid");
            },
        });

        function removePreview() {
            setTimeout(function () {
                if ($(".k-window #previewWindow").length > 0) {
                    $("#previewWindow").parent().remove();
                    $(gridId).after("<div id='previewWindow'></div>");
                }
            }, 200);
        }

        previewWindow.kendoWindow({
            width: "1400px",
            height: "800px",
            title: "PREVIEW - TỔNG HỢP ĐƠN",
            visible: false,
            actions: ["Close"],
            resizable: true,
            draggable: true,
            modal: true,
            close: function (e) {
                removePreview();
            },
        }).data("kendoWindow").center();

        // Bind button events
        $("#confirmCreatePA").kendoButton({
            icon: "check"
        }).click(function () {
            previewWindow.data("kendoWindow").close();
            createPAGroupFromPO();
        });

        $("#cancelPreview").kendoButton({
            icon: "cancel"
        }).click(function () {
            previewWindow.data("kendoWindow").close();
        });

        previewWindow.data("kendoWindow").open();
    }

    function createPAGroupFromPO() {
        ajax("POST", "/PurchaseAgreement/CreatePAGroup", {}, function (response) {
            if (response.isSuccess) {
                showSuccessMessages(["Tạo tổng hợp đơn thành công!"]);
                $(gridId).data("kendoGrid").dataSource.filter({});
            } else {
                showErrorMessages(response.errorMessageList);
            }
        }, null, false);
    }

    function showEditablePAGroupPreviewModal(data) {
        let previewWindow = $("#previewWindow");
        let previewHtml = '<div class="editable-pa-group-preview">' +
            '<div class="preview-header">' +
            '<h5 style="color: #007bff; margin-bottom: 15px;">PREVIEW - TỔNG HỢP ĐƠN CÓ THỂ CHỈNH SỬA</h5>' +
            '</div>' +
            '<div class="pa-info-row">' +
            '<div class="pa-info-col">' +
            '<p><strong>Mã nhóm:</strong> ' + (data.groupCode || '') + '</p>' +
            '<p><strong>Số sản phẩm:</strong> ' + (data.productVendorMappings ? data.productVendorMappings.length : 0) + '</p>' +
            '</div>' +
            '<div class="pa-info-col">' +
            '<p><strong>Tổng tiền:</strong> <span id="totalPriceDisplay">' + kendo.toString(data.totalPrice || 0, "n0") + ' VNĐ</span></p>' +
            '<p><strong>Trạng thái:</strong> ' + (data.statusName || 'Mới') + '</p>' +
            '</div>' +
            '</div>' +
            '<hr>' +
            '<h5>Danh sách sản phẩm và nhà cung cấp:</h5>' +
            '<div id="editableProductVendorGrid"></div>' +
            '<div class="preview-actions" style="margin-top: 20px; text-align: center;">' +
            '<button id="confirmCreateEditablePA" class="k-button k-button-md k-rounded-md k-button-solid k-button-solid-success" style="margin-right: 10px;">' +
            '<span class="k-icon k-i-check k-button-icon"></span>' +
            '<span class="k-button-text">Xác nhận tạo</span>' +
            '</button>' +
            '<button id="cancelEditablePreview" class="k-button k-button-md k-rounded-md k-button-solid k-button-solid-base">' +
            '<span class="k-icon k-i-cancel k-button-icon"></span>' +
            '<span class="k-button-text">Hủy</span>' +
            '</button>' +
            '</div>' +
            '</div>';

        previewWindow.html(previewHtml);

        // Initialize the editable grid
        var gridData = data.productVendorMappings || [];

        // Debug: Log the data structure to see actual property names
        console.log("Grid data:", gridData);
        if (gridData.length > 0) {
            console.log("First item availableVendors:", gridData[0].availableVendors);
            if (gridData[0].availableVendors && gridData[0].availableVendors.length > 0) {
                console.log("First vendor structure:", gridData[0].availableVendors[0]);
                console.log("Vendor keys:", Object.keys(gridData[0].availableVendors[0]));
            }
        }



        $("#editableProductVendorGrid").kendoGrid({
            dataSource: {
                data: gridData,
                schema: {
                    model: {
                        fields: {
                            product_ID: { type: "number" },
                            productCode: { type: "string" },
                            productName: { type: "string" },
                            totalQuantity: { type: "number" },
                            unitName: { type: "string" },
                            price: { type: "number" },
                            totalAmount: { type: "number" },
                            vendor_ID: { type: "number" },
                            vendorName: { type: "string" }
                        }
                    }
                }
            },
            columns: [
                { field: "productCode", title: "Mã SP", width: "100px", template: "#= productCode || 'N/A' #" },
                { field: "productName", title: "Tên sản phẩm", width: "200px", template: "#= productName || 'N/A' #" },
                { field: "totalQuantity", title: "Số lượng", width: "100px", template: "#= kendo.toString(totalQuantity || 0, 'n2') #", attributes: { style: "text-align: center;" } },
                { field: "unitName", title: "Đơn vị", width: "80px", template: "#= unitName || 'N/A' #", attributes: { style: "text-align: center;" } },
                { field: "price", title: "Đơn giá", width: "120px", template: "#= kendo.toString(price || 0, 'n0') # VNĐ", attributes: { style: "text-align: right;" } },
                {
                    title: "Thành tiền",
                    width: "120px",
                    template: "#= kendo.toString(parseFloat(totalQuantity || 0) * parseFloat(price || 0), 'n0') # VNĐ",
                    attributes: { style: "text-align: right;" }
                },
                {
                    field: "vendor_ID",
                    title: "Nhà cung cấp",
                    width: "200px",
                    template: function (dataItem) {
                        if (!dataItem || typeof dataItem.product_ID === 'undefined' || dataItem.product_ID === null) {
                            return '<span style="color: red;">Lỗi: Không có product_ID</span>';
                        }

                        var dropdownId = "vendorDropdown_" + dataItem.product_ID;
                        var options = '';

                        if (dataItem.availableVendors && Array.isArray(dataItem.availableVendors) && dataItem.availableVendors.length > 0) {
                            dataItem.availableVendors.forEach(function (vendor) {
                                if (vendor && vendor.id) {
                                    var selected = vendor.id == dataItem.vendor_ID ? 'selected' : '';
                                    var displayText = vendor.text || vendor.name || 'Vendor ' + vendor.id;
                                    var vendorPrice = vendor.price || 0;
                                    var priceText = vendorPrice > 0 ? ' - ' + kendo.toString(vendorPrice, 'n0') + ' VNĐ' : ' - Chưa có giá';
                                    options += '<option value="' + vendor.id + '" ' + selected + '>' + displayText + priceText + '</option>';
                                }
                            });
                        }

                        if (options === '') {
                            options = '<option value="">Không có nhà cung cấp</option>';
                        }

                        return '<select id="' + dropdownId + '" class="vendor-dropdown" data-product-id="' + dataItem.product_ID + '">' + options + '</select>';
                    }
                }
            ],
            pageable: false,
            scrollable: true,
            height: 400,
            dataBound: function () {
                // Initialize vendor dropdowns after grid is bound
                $(".vendor-dropdown").each(function () {
                    var $dropdown = $(this);
                    var productId = $dropdown.data("product-id");

                    // Find the current data from the original data source
                    var currentData = null;
                    if (data && data.productVendorMappings) {
                        currentData = data.productVendorMappings.find(m => m.product_ID === productId);
                    }

                    if (currentData && currentData.availableVendors && currentData.availableVendors.length > 0) {


                        $dropdown.kendoDropDownList({
                            dataTextField: "text",
                            dataValueField: "id",
                            dataSource: currentData.availableVendors,
                            value: currentData.vendor_ID,
                            template: function(dataItem) {
                                if (!dataItem) return "";
                                var displayText = dataItem.text || dataItem.name || 'Vendor ' + dataItem.id;
                                var vendorPrice = dataItem.price || 0;
                                var priceText = vendorPrice > 0
                                    ? '<span style="color: #28a745; font-weight: bold;"> - ' + kendo.toString(vendorPrice, 'n0') + ' VNĐ</span>'
                                    : '<span style="color: #dc3545; font-style: italic;"> - Chưa có giá</span>';
                                return displayText + priceText;
                            },
                            valueTemplate: function(dataItem) {
                                if (!dataItem) return "";
                                var displayText = dataItem.text || dataItem.name || 'Vendor ' + dataItem.id;
                                var vendorPrice = dataItem.price || 0;
                                var priceText = vendorPrice > 0
                                    ? ' - ' + kendo.toString(vendorPrice, 'n0') + ' VNĐ'
                                    : ' - Chưa có giá';
                                return displayText + priceText;
                            },
                            change: function (e) {
                                updateVendorMapping(productId, e.sender.value());
                            }
                        });
                    } else {
                        // Show a simple text instead of dropdown if no vendors
                        $dropdown.replaceWith('<span>Không có nhà cung cấp</span>');
                    }
                });

                loadMobile("#editableProductVendorGrid");
            }
        });



        function updateVendorMapping(productId, newVendorId) {
            console.log("updateVendorMapping called:", productId, newVendorId);

            // Find and update the mapping
            var mapping = data.productVendorMappings.find(m => m.product_ID === productId);
            console.log("Found mapping:", mapping);

            if (mapping) {
                var oldPrice = mapping.price || 0; // Store old price for comparison
                console.log("Old price:", oldPrice);

                mapping.vendor_ID = newVendorId;
                var newVendor = mapping.availableVendors.find(v => v.id == newVendorId);
                console.log("Found new vendor:", newVendor);

                if (newVendor) {
                    console.log("New vendor keys:", Object.keys(newVendor));
                    mapping.vendorName = newVendor.name;

                    // Get new price from vendor data (no API call needed!)
                    var newPrice = newVendor.price || 0;
                    console.log("New price:", newPrice);

                    mapping.price = newPrice;

                    // Update the grid dataSource and refresh to show new price
                    var grid = $("#editableProductVendorGrid").data("kendoGrid");
                    if (grid) {
                        // Update the dataSource with new data
                        grid.dataSource.data(data.productVendorMappings);
                        grid.refresh();
                    }

                    // Update total price display
                    var newTotalPrice = data.productVendorMappings.reduce((sum, m) => sum + (parseFloat(m.totalQuantity || 0) * parseFloat(m.price || 0)), 0);
                    $("#totalPriceDisplay").text(kendo.toString(newTotalPrice, "n0") + " VNĐ");

                    // Show notification about price change
                    var productName = mapping.productName || "Sản phẩm";
                    var vendorName = newVendor.name || "nhà cung cấp";
                    var priceChangeMessage = "";

                    if (newPrice === 0) {
                        priceChangeMessage = productName + " - " + vendorName + ": Chưa có giá được thiết lập";
                        showErrorMessages([priceChangeMessage]);
                    } else if (oldPrice !== newPrice) {
                        priceChangeMessage = productName + " - " + vendorName + ": Giá đã thay đổi từ " + kendo.toString(oldPrice, "n0") + " VNĐ → " + kendo.toString(newPrice, "n0") + " VNĐ";
                        showSuccessMessages([priceChangeMessage]);
                    } else {
                        priceChangeMessage = productName + " - " + vendorName + ": Giá không thay đổi (" + kendo.toString(newPrice, "n0") + " VNĐ)";
                        showSuccessMessages([priceChangeMessage]);
                    }
                }
            }
        }

        function removeEditablePreview() {
            setTimeout(function () {
                if ($(".k-window #previewWindow").length > 0) {
                    $("#previewWindow").parent().remove();
                    $(gridId).after("<div id='previewWindow'></div>");
                }
            }, 200);
        }

        previewWindow.kendoWindow({
            width: "1400px",
            height: "800px",
            title: "PREVIEW - TỔNG HỢP ĐƠN CÓ THỂ CHỈNH SỬA",
            visible: false,
            actions: ["Close"],
            resizable: true,
            draggable: true,
            modal: true,
            close: function (e) {
                removeEditablePreview();
            },
        }).data("kendoWindow").center();

        // Bind button events
        $("#confirmCreateEditablePA").kendoButton({
            icon: "check"
        }).click(function () {
            previewWindow.data("kendoWindow").close();
            createPAGroupWithCustomMapping(data.productVendorMappings);
        });

        $("#cancelEditablePreview").kendoButton({
            icon: "cancel"
        }).click(function () {
            previewWindow.data("kendoWindow").close();
        });

        previewWindow.data("kendoWindow").open();
    }

    function createPAGroupWithCustomMapping(productVendorMappings) {
        var request = {
            productVendorMappings: productVendorMappings
        };

        ajax("POST", "/PurchaseAgreement/CreatePAGroupWithCustomMapping", request, function (response) {
            if (response.isSuccess) {
                showSuccessMessages(["Tạo tổng hợp đơn thành công!"]);
                $(gridId).data("kendoGrid").dataSource.filter({});
            } else {
                showErrorMessages(response.errorMessageList || ["Có lỗi xảy ra khi tạo tổng hợp đơn"]);
            }
        }, null, false);
    }

    async function ExportExcel() {
        var searchModel = getSearchModel();
        var params = new URLSearchParams(searchModel).toString();
        window.open("/PurchaseAgreement/ExportPAGroupExcel?" + params, '_blank');
    }

    function getSearchModel() {
        let searchString = $("#searchString").val();
        let vendorId = $("#vendorDropdown").data("kendoDropDownList")?.value();
        let status = $("#statusDropdown").data("kendoDropDownList")?.value();
        let dateFrom = $("#dateFrom").data("kendoDatePicker")?.value();
        let dateTo = $("#dateTo").data("kendoDatePicker")?.value();

        return {
            searchString,
            vendor_ID: vendorId && vendorId !== "" ? vendorId : null,
            status: status && status !== "" ? status : null,
            dateFrom: dateFrom ? kendo.toString(dateFrom, "yyyy-MM-ddTHH:mm:ss") : kendo.toString(oneWeekAgo, "yyyy-MM-ddTHH:mm:ss") ,
            dateTo: dateTo ? kendo.toString(dateTo, "yyyy-MM-ddTHH:mm:ss") : kendo.toString(today, "yyyy-MM-ddTHH:mm:ss") 
        };
    }

    function InitGrid() {
        let htmlToolbar = `
                <div id='toolbar' style=''  class='w-100 d-flex flex-column'>
                       <div class="row gx-0 row-gap-2 w-100">
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="searchString">Tìm kiếm:</label>
                                    <input type="text" class=" w-100" id="searchString"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="vendorDropdown">Nhà cung cấp:</label>
                                    <select id="vendorDropdown" class="w-100"></select>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="statusDropdown">Trạng thái:</label>
                                    <select id="statusDropdown" class="w-100"></select>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="dateFrom">Từ ngày:</label>
                                    <input type="date" class="w-100" id="dateFrom"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="dateTo">Đến ngày:</label>
                                    <input type="date" class="w-100" id="dateTo"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 d-flex align-items-end">
                                <div class="pe-1 d-flex gap-2">
                                    <button id="search" title="Tìm kiếm" class = "k-button k-button-md k-rounded-md k-button-solid k-button-solid-primary  k-icon-button"><span class='k-icon k-i-search k-button-icon'></span><span class='k-button-text d-none'>Tìm kiếm</span></button>
                                    <button id='createPAGroupWithEditablePreview' title="Tạo tổng hợp đơn (có thể chỉnh sửa)"  class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-success _permission_' data-enum='44' ><span class='k-icon k-i-plus k-button-icon'></span><span class='k-button-text'>Tạo tổng hợp đơn</span></button>
                                    <button id='createPAGroupWithPreview' title="Tạo tổng hợp đơn (tự động)"  class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-info _permission_' data-enum='44' ><span class='k-icon k-i-plus k-button-icon'></span><span class='k-button-text'>Tạo tự động</span></button>
                                    <button id="exportExcel" class="k-button k-button-md k-rounded-md k-button-outline k-button-outline-error"><span class="k-icon k-i-file-excel k-button-icon"></span><span class="k-button-text">Export Excel</span></button>
                                </div>
                            </div>
                        </div>
                </div>
            `;

        $(gridId).kendoGrid({
            dataSource: {
                transport: {
                    read: {
                        url: "/PurchaseAgreement/GetPAGroupList",
                        datatype: "json",
                    },
                    parameterMap: function (data, type) {
                        if (type == "read") {
                            var searchModel = getSearchModel();
                            return {
                                ...searchModel,
                                pageSize: data.pageSize,
                                pageNumber: data.page
                            }
                        }
                    },
                },
                serverPaging: true,
                serverFiltering: true,
                page: 1,
                pageSize: 20,
                schema: {
                    type: 'json',
                    parse: function (response) {
                        if (response.isSuccess == false) {
                            showErrorMessages(response.errorMessageList);
                            return {
                                data: [],
                                total: 0
                            }
                        }
                        return response.data;
                    },
                    model: {
                        id: "groupCode",
                        fields: {
                            createdDate: { type: "date" },
                            updatedDate: { type: "date" },
                            stt: { type: "number" },
                            totalPrice: { type: "number" },
                            vendorCount: { type: "number" },
                            totalItemCount: { type: "number" }
                        }
                    },
                    data: "data",
                    total: "total"
                },
            },
            selectable: true,
            pageable: {
                pageSizes: [10, 20, 50],
            },
            dataBinding: function (e) {
                record = (this.dataSource._page - 1) * this.dataSource._pageSize;
            },
            toolbar: htmlToolbar,
            columns: [
                {
                    field: "",
                    title: "STT",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: "#: ++record #",
                    width: 50
                },
                {
                    field: "groupCode",
                    title: "Mã nhóm",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: 150,
                },
                {
                    field: "vendorCount",
                    title: "Số NCC",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: 50,
                },
                {
                    field: "totalPrice",
                    title: "Tổng tiền",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:right;" },
                    template: "#= kendo.toString(totalPrice, 'n0') # VNĐ",
                    width: 200,
                },
                {
                    field: "statusName",
                    title: "Trạng thái",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: 120,
                    template: function (dataItem) {
                        // Add null check for dataItem
                        if (!dataItem) {
                            return '<span class="status-badge new">N/A</span>';
                        }

                        let statusClass = "";
                        // Status có thể được lưu dưới dạng enum name hoặc enum value
                        switch (dataItem.status) {
                            case "New":
                            case "0":
                                statusClass = "new"; break; // Mới
                            case "SendVendor":
                            case "1":
                                statusClass = "sendvendor"; break;   // Đã gửi NCC
                            case "EmailFailed":
                            case "2":
                                statusClass = "emailfailed"; break;    // Gửi email thất bại
                            case "PartialEmailSent":
                            case "3":
                                statusClass = "partialemailsent"; break;    // Gửi email một phần
                            case "Cancel":
                            case "4":
                                statusClass = "cancel"; break;    // Hủy
                            case "Completed":
                            case "5":
                                statusClass = "completed"; break;   // Hoàn thành
                            default: statusClass = "new";
                        }
                        return `<span class="status-badge ${statusClass}">${dataItem.statusName || 'N/A'}</span>`;
                    }
                },
                {
                    field: "createdDate",
                    title: "Ngày tạo",
                    width: 180,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: '#: createdDate ? kendo.toString(kendo.parseDate(createdDate), "dd/MM/yyyy HH:mm") : "" #',
                },
                {
                    field: "updatedDate",
                    title: "Ngày cập nhật",
                    width: 180,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: '#: updatedDate ? kendo.toString(kendo.parseDate(updatedDate), "dd/MM/yyyy HH:mm") : "" #',
                },
                {
                    field: "", title: "Thao tác", width: "350px", attributes: { style: "text-align: center;" },
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    template: function (dataItem) {
                        var completeButton = '';
                        var sendButton = '';

                        // Show send button only for orders with status "New" (0)
                        if (dataItem.status === "New" || dataItem.status === "0" || dataItem.status === 0) {
                            sendButton = '<button onclick="sendToVendors(\'' + dataItem.groupCode + '\')" title="Gửi Email NCC" class="btn-action btn-send _permission_" data-enum="44">' +
                                '<i class="fas fa-envelope"></i>' +
                                '</button>';
                        }

                        // Show complete button only for orders with status "SendVendor" (1)
                        if (dataItem.status === "SendVendor" || dataItem.status === "1" || dataItem.status === 1) {
                            completeButton = '<button onclick="completePAGroup(\'' + dataItem.groupCode + '\')" title="Hoàn thành" class="btn-action btn-complete _permission_" data-enum="44">' +
                                '<i class="fas fa-check"></i>' +
                                '</button>';
                        }

                        // Show cancel button for orders with specific statuses (New, SendVendor, EmailFailed, PartialEmailSent)
                        var cancelButton = '';
                        if (dataItem.status === "New" || dataItem.status === "0" || dataItem.status === 0 ||
                            dataItem.status === "SendVendor" || dataItem.status === "1" || dataItem.status === 1 ||
                            dataItem.status === "EmailFailed" || dataItem.status === "2" || dataItem.status === 2 ||
                            dataItem.status === "PartialEmailSent" || dataItem.status === "3" || dataItem.status === 3) {
                            cancelButton = '<button onclick="cancelPAGroup(\'' + dataItem.groupCode + '\')" title="Hủy PA Group" class="btn-action btn-cancel _permission_" data-enum="44">' +
                                '<i class="fas fa-ban"></i>' +
                                '</button>';
                        }

                        // PAGroup export buttons - always available
                        var groupExportButtons = '<button onclick="exportPAGroupPdf(\'' + dataItem.groupCode + '\')" title="Xuất PDF theo nhóm" class="btn-action btn-export-pdf">' +
                            '<i class="fas fa-file-pdf"></i>' +
                            '</button>' +
                            '<button onclick="exportPAGroupExcel(\'' + dataItem.groupCode + '\')" title="Xuất Excel theo nhóm" class="btn-action btn-export-excel">' +
                            '<i class="fas fa-file-excel"></i>' +
                            '</button>';

                        return '<div class="action-buttons">' +
                            '<button onclick="viewPAGroupDetail(\'' + dataItem.groupCode + '\')" title="Xem chi tiết" class="btn-action btn-view _permission_" data-enum="45">' +
                            '<i class="fas fa-eye"></i>' +
                            '</button>' +
                            sendButton +
                            completeButton +
                            cancelButton +
                            groupExportButtons +
                            '</div>';
                    }
                }
            ],
            scrollable: true,
            dataBound: function (e) {
                CheckPermission();loadMobile(gridId);
            }
        });
    }

    function InitKendoToolBar() {
        

        // Initialize Date Pickers
        $("#dateFrom").kendoDatePicker({
            format: "dd/MM/yyyy",
            culture: "vi-VN",
            value: oneWeekAgo,
        });

        $("#dateTo").kendoDatePicker({
            format: "dd/MM/yyyy",
            culture: "vi-VN",
            value: today
        });

        $("#search").kendoButton({
            icon: "search"
        });
        $("#search").click(async function (e) {
            var grid = $(gridId).data("kendoGrid");
            grid.dataSource.filter({});
        });
        $("#exportExcel").click(async function (e) {
            ExportExcel();
        });
        $("#searchString").kendoTextBox({
            icon: {
                type: "search",
                position: "end"
            },
            placeholder: "Nhập mã nhóm tìm kiếm..."
        });

        // Initialize Vendor Dropdown
        $("#vendorDropdown").kendoDropDownList({
            dataTextField: "text",
            dataValueField: "value",
            optionLabel: "-- Chọn nhà cung cấp --",
            template: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "N/A";
                }
                return dataItem.text;
            },
            valueTemplate: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "-- Chọn nhà cung cấp --";
                }
                return dataItem.text;
            },
            dataSource: {
                transport: {
                    read: {
                        url: "/Common/GetDataOptionsDropdown",
                        data: { type: "Vendor" }
                    }
                },
                schema: {
                    parse: function (response) {
                        if (response && response.isSuccess && response.data) {
                            return response.data.filter(function (item) {
                                return item && item.text && item.value !== undefined;
                            });
                        }
                        return [];
                    }
                }
            }
        });

        // Initialize Status Dropdown
        $("#statusDropdown").kendoDropDownList({
            dataTextField: "text",
            dataValueField: "dataRaw",
            optionLabel: "-- Chọn trạng thái --",
            template: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "N/A";
                }
                return dataItem.text;
            },
            valueTemplate: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "-- Chọn trạng thái --";
                }
                return dataItem.text;
            },
            dataSource: {
                transport: {
                    read: {
                        url: "/Common/GetDataOptionsDropdown",
                        data: { type: "PAStatus" }
                    }
                },
                schema: {
                    parse: function (response) {
                        if (response && response.isSuccess && response.data) {
                            return response.data.filter(function (item) {
                                return item && item.text && item.dataRaw !== undefined;
                            });
                        }
                        return [];
                    }
                }
            }
        });


        $("#createPAGroupWithEditablePreview").kendoButton({
            icon: "plus"
        });

        $("#createPAGroupWithEditablePreview").on('click', function () {
            showEditablePAGroupPreview();
        });

        $("#createPAGroupWithPreview").kendoButton({
            icon: "plus"
        });

        $("#createPAGroupWithPreview").on('click', function () {
            showPAGroupPreview();
        });

    };

</script>
<script type="text/javascript">
    InitGrid();
    InitKendoToolBar();
    $(document).ready(function () {
        $(window).trigger("resize");
    });
</script>
<style>
    .k-form-buttons {
        justify-content: flex-end;
    }

    .pa-detail-container {
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
        margin: 10px 0;
    }

        .pa-detail-container h6 {
            color: #495057;
            margin-bottom: 15px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 8px;
        }

    .pa-items-detail {
        margin-top: 15px;
    }

        .pa-items-detail h6 {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 10px;
        }

    .pa-info-row {
        display: flex;
        gap: 20px;
        margin-bottom: 15px;
    }

    .pa-info-col {
        flex: 1;
        min-width: 0;
    }

        .pa-info-col p {
            margin-bottom: 8px;
            word-wrap: break-word;
        }

    .pa-group-preview {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
    }

    .preview-header {
        text-align: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid #007bff;
    }

    .preview-actions {
        background-color: #ffffff;
        padding: 15px;
        border-radius: 5px;
        border: 1px solid #dee2e6;
    }

        .preview-actions .k-button {
            min-width: 120px;
        }

    /* Complete button styling */
    .k-button-solid-warning {
        background-color: #ffc107;
        border-color: #ffc107;
        color: #212529;
    }

        .k-button-solid-warning:hover {
            background-color: #e0a800;
            border-color: #d39e00;
            color: #212529;
        }

    @@media (max-width: 768px) {
        .pa-info-row {
            flex-direction: column;
            gap: 10px;
        }

        .preview-actions .k-button {
            width: 100%;
            margin-bottom: 10px;
        }
    }

    /* Action buttons styling to match PurchaseOrder/Index */
    .action-buttons {
        display: flex;
        gap: 3px;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        min-width: 350px;
    }

    /* Ensure action column has proper width */
    .k-grid-header th:last-child,
    .k-grid-content td:last-child {
        min-width: 350px !important;
        width: 350px !important;
    }

    .btn-action {
        border: none;
        border-radius: 4px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        transition: all 0.2s ease;
        color: white;
    }

        .btn-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .btn-action.btn-view {
            background-color: #17a2b8;
            border-color: #17a2b8;
        }

            .btn-action.btn-view:hover {
                background-color: #138496;
                border-color: #117a8b;
            }

        .btn-action.btn-send {
            background-color: #28a745;
            border-color: #28a745;
        }

            .btn-action.btn-send:hover {
                background-color: #218838;
                border-color: #1e7e34;
            }



        .btn-action.btn-complete {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #212529;
        }

            .btn-action.btn-complete:hover {
                background-color: #e0a800;
                border-color: #d39e00;
                color: #212529;
            }

        .btn-action.btn-cancel {
            background-color: #dc3545;
            border-color: #dc3545;
        }

            .btn-action.btn-cancel:hover {
                background-color: #c82333;
                border-color: #bd2130;
            }





    /* Editable preview specific styling */
    .editable-pa-group-preview {
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
    }

        .editable-pa-group-preview .preview-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #007bff;
        }

    .vendor-dropdown {
        width: 100%;
        min-width: 150px;
    }

    #editableProductVendorGrid .k-grid-content {
        overflow-x: auto;
    }

    #editableProductVendorGrid .k-grid-header th {
        white-space: nowrap;
    }

    #totalPriceDisplay {
        font-weight: bold;
        color: #28a745;
    }

    /* Status badge styling */
    .status-badge {
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        display: inline-block;
        min-width: 80px;
    }

        .status-badge.new {
            background-color: #007bff;
            color: white;
        }

        .status-badge.sendvendor {
            background-color: #28a745;
            color: white;
        }

        .status-badge.emailfailed {
            background-color: #dc3545;
            color: white;
            border: 2px solid #fd7e14;
        }

        .status-badge.partialemailsent {
            background-color: #fd7e14;
            color: white;
            border: 2px solid #ffc107;
        }

        .status-badge.completed {
            /* background-color: #007bff;
            color: white; */
            border: 1px solid #007bff;
            color: #007bff;
        }

        .status-badge.cancel {
            background-color: #dc3545;
            color: white;
        }

    /* Single PA resend email button styling */
    .btn-action.btn-resend-email-single {
        background-color: #6f42c1;
        border-color: #6f42c1;
        padding: 4px 8px;
        font-size: 12px;
    }

        .btn-action.btn-resend-email-single:hover {
            background-color: #5a32a3;
            border-color: #512da8;
        }

        .btn-action.btn-resend-email-single i {
            font-size: 12px;
        }

    /* PA Export buttons styling - grouped with email button */
    .btn-action.btn-export-pdf {
        background-color: #dc3545;
        border-color: #dc3545;
    }

        .btn-action.btn-export-pdf:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }

    .btn-action.btn-export-excel {
        background-color: #28a745;
        border-color: #28a745;
    }

        .btn-action.btn-export-excel:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }

    .btn-action.btn-export-word {
        background-color: #007bff;
        border-color: #007bff;
    }

        .btn-action.btn-export-word:hover {
            background-color: #0069d9;
            border-color: #0062cc;
        }

    .btn-action.btn-resend-email {
        background-color: #6f42c1;
        border-color: #6f42c1;
        color: white;
    }

        .btn-action.btn-resend-email:hover {
            background-color: #5a32a3;
            border-color: #512d92;
        }

</style>
