# HƯỚNG DẪN SỬ DỤNG HỆ THỐNG QUẢN LÝ ĐƠN HÀNG TASIN

## 1. GIỚI THIỆU TỔNG QUAN

### 1.1 <PERSON><PERSON><PERSON> đích
Hệ thống Tasin là một ứng dụng web quản lý đơn hàng toàn diện, hỗ trợ các doanh nghiệp trong việc quản lý:
- <PERSON><PERSON><PERSON> cung cấp (Vendor/Supplier)
- <PERSON><PERSON><PERSON><PERSON> (Customer)
- <PERSON><PERSON><PERSON> phẩm/<PERSON><PERSON><PERSON> hóa (Product)
- <PERSON><PERSON><PERSON> hàng mua (Purchase Order)
- Phiếu giao hàng (Delivery Slip)
- Thống kê và báo cáo

### 1.2 Công nghệ sử dụng
- Framework: ASP.NET Core MVC
- Database: SQL Server với Entity Framework Core
- UI Framework: Kendo UI
- Authentication: Cookie-based với JWT support
- Authorization: Role-based access control

## 2. ĐĂNG NHẬP VÀ XÁC THỰC

### 2.1 Đăng nhập hệ thống
1. <PERSON><PERSON><PERSON> cập trang đăng nhập của hệ thống
2. Nhập tên đăng nhập và mật khẩu
3. <PERSON><PERSON><PERSON> đăng nhập sai quá 3 lần, hệ thống sẽ yêu cầu nhập mã captcha
4. Sau khi đăng nhập thành công, hệ thống sẽ chuyển hướng đến trang chủ

### 2.2 Phân quyền người dùng
Hệ thống có các loại quyền chính:
- **System Admin**: Quyền cao nhất, quản lý toàn bộ hệ thống
- **Admin**: Quản lý các chức năng nghiệp vụ
- **User**: Người dùng thường với quyền hạn chế

### 2.3 Đăng xuất
Nhấn vào tên người dùng ở góc phải màn hình và chọn "Đăng xuất"

## 3. GIAO DIỆN CHÍNH

### 3.1 Menu điều hướng
Menu bên trái bao gồm các module chính:
- **Người dùng**: Quản lý tài khoản người dùng
- **Nhà cung cấp**: Quản lý thông tin nhà cung cấp
- **Khách hàng**: Quản lý thông tin khách hàng  
- **Hàng hóa**: Quản lý sản phẩm
- **Đơn vị tính**: Quản lý đơn vị đo lường
- **Danh mục**: Phân loại sản phẩm
- **Nguyên liệu**: Quản lý nguyên liệu
- **Đơn hàng PO**: Quản lý đơn hàng mua
- **Thống kê**: Các báo cáo thống kê

### 3.2 Thanh công cụ
Mỗi trang quản lý có thanh công cụ với các nút:
- **Thêm mới**: Tạo bản ghi mới (màu xanh lá)
- **Sửa**: Chỉnh sửa bản ghi đã chọn (màu xanh dương)
- **Xóa**: Xóa bản ghi đã chọn (màu đỏ)
- **Export Excel**: Xuất dữ liệu ra file Excel

## 4. QUẢN LÝ NHÀ CUNG CẤP

### 4.1 Xem danh sách nhà cung cấp
1. Vào menu "Nhà cung cấp"
2. Danh sách hiển thị với các cột: Mã, Tên, Địa chỉ, Email, Số điện thoại, Mã số thuế
3. Sử dụng ô tìm kiếm để lọc dữ liệu

### 4.2 Thêm nhà cung cấp mới
1. Nhấn nút "Thêm mới"
2. Điền thông tin:
   - Tên nhà cung cấp (bắt buộc)
   - Địa chỉ
   - Email
   - Số điện thoại
   - Mã số thuế
3. Nhấn "Lưu" để hoàn tất

### 4.3 Quản lý sản phẩm của nhà cung cấp
1. Chọn nhà cung cấp và nhấn "Sửa"
2. Trong form chi tiết, có tab "Sản phẩm"
3. Nhấn "Thêm sản phẩm" để thêm sản phẩm mới
4. Điền thông tin: Sản phẩm, Giá, Độ ưu tiên, Mô tả
5. Sử dụng tính năng tìm kiếm để tìm sản phẩm đã có

### 4.4 Tính năng đặc biệt
- **Tính giá tự động**: Nhấn nút trong ô giá để mở popup tính giá với % lợi nhuận
- **Lịch sử giá**: Hệ thống tự động lưu lịch sử thay đổi giá sản phẩm
- **Import Excel**: Tải file mẫu và import hàng loạt

## 5. QUẢN LÝ KHÁCH HÀNG

### 5.1 Thêm khách hàng
1. Vào menu "Khách hàng" → "Thêm mới"
2. Điền thông tin cơ bản:
   - Tên khách hàng (bắt buộc)
   - Loại khách hàng
   - Số điện thoại liên hệ
   - Email
   - Địa chỉ
   - Mã số thuế

### 5.2 Thông tin thanh toán
Hệ thống hỗ trợ lưu thông tin thanh toán:
- Số tài khoản ngân hàng
- Tên ngân hàng  
- Phương thức thanh toán (Tiền mặt/Chuyển khoản/Thẻ)

### 5.3 Tích hợp với đơn hàng
Khi tạo đơn hàng, thông tin thanh toán sẽ tự động load từ thông tin khách hàng

## 6. QUẢN LÝ SẢN PHẨM

### 6.1 Thêm sản phẩm mới
1. Vào "Hàng hóa" → "Thêm mới"
2. Điền thông tin:
   - Tên sản phẩm (bắt buộc)
   - Danh mục
   - Đơn vị tính
   - Giá mặc định
   - Mô tả

### 6.2 Quản lý giá sản phẩm
- **Giá mặc định**: Giá cơ bản của sản phẩm
- **Lịch sử giá**: Xem trang "Thống kê biến động giá hàng hóa"
- **Thống kê giá**: Giá cao nhất, thấp nhất, trung bình theo khoảng thời gian

### 6.3 Phân loại sản phẩm
- Sử dụng "Danh mục" để tạo các nhóm sản phẩm
- Quản lý "Đơn vị tính" (kg, lít, cái, thùng...)
- Quản lý "Nguyên liệu" cho sản phẩm

## 7. QUẢN LÝ ĐƠN HÀNG

### 7.1 Tạo đơn hàng mới (Purchase Order)
1. Vào "Đơn hàng PO" → "Thêm mới"
2. Chọn khách hàng (thông tin thanh toán tự động load)
3. Thêm sản phẩm vào đơn hàng:
   - Chọn sản phẩm từ dropdown
   - Nhập số lượng
   - Giá sẽ tự động tính toán
   - Có thể điều chỉnh: tỷ lệ hao hụt, thuế, phí gia công, chi phí phụ

### 7.2 Tính toán trong đơn hàng
Hệ thống tự động tính:
- Thành tiền = Số lượng × Giá × (1 + Tỷ lệ hao hụt) + Chi phí phụ + Phí gia công
- Thuế = Thành tiền × Tỷ lệ thuế
- Tổng tiền = Thành tiền + Thuế

### 7.3 Trạng thái đơn hàng
- **Nháp**: Đơn hàng chưa xác nhận
- **Đã xác nhận**: Đơn hàng đã được duyệt
- **Đang giao**: Đơn hàng đang trong quá trình giao hàng
- **Hoàn thành**: Đơn hàng đã hoàn tất

### 7.4 Xuất báo cáo
- **Hóa đơn PDF**: Xuất hóa đơn định dạng PDF
- **Hóa đơn Excel**: Xuất hóa đơn định dạng Excel  
- **Phiếu giao hàng**: Xuất phiếu giao hàng (chỉ gồm STT, Tên SP, Số lượng, Thành tiền)

## 8. THỐNG KÊ VÀ BÁO CÁO

### 8.1 Thống kê sản phẩm đặt hàng
- Xem số lượng, giá trị đặt hàng theo sản phẩm
- Lọc theo khoảng thời gian
- Xuất Excel để phân tích

### 8.2 Thống kê khách hàng đặt hàng  
- Xem tổng giá trị đơn hàng theo khách hàng
- Phân tích xu hướng mua hàng
- Biểu đồ trực quan

### 8.3 Thống kê biến động giá
1. Vào "Thống kê biến động giá hàng hóa"
2. Chọn sản phẩm và khoảng thời gian
3. Xem:
   - Giá cao nhất, thấp nhất, trung bình
   - Biểu đồ đường thể hiện xu hướng giá
   - Lịch sử thay đổi giá chi tiết

## 9. TÍNH NĂNG NÂNG CAO

### 9.1 Responsive Design
- Giao diện tự động điều chỉnh trên mobile
- Bảng dữ liệu có thể cuộn ngang trên màn hình nhỏ
- Tất cả chức năng đều hoạt động trên thiết bị di động

### 9.2 Tìm kiếm và lọc dữ liệu
- Tìm kiếm nhanh trên mọi trang danh sách
- Lọc theo nhiều tiêu chí
- Sắp xếp dữ liệu theo cột

### 9.3 Xuất dữ liệu
- Export Excel cho tất cả danh sách
- Export PDF cho đơn hàng và báo cáo
- Template Excel để import dữ liệu

### 9.4 Bảo mật
- Phân quyền chi tiết theo chức năng
- Mã hóa mật khẩu SHA-256
- Session timeout tự động
- Audit trail cho các thao tác quan trọng

## 10. XỬ LÝ SỰ CỐ

### 10.1 Quên mật khẩu
Liên hệ quản trị viên để reset mật khẩu

### 10.2 Lỗi khi lưu dữ liệu
- Kiểm tra kết nối mạng
- Đảm bảo các trường bắt buộc đã điền đầy đủ
- Refresh trang và thử lại

### 10.3 Không thể truy cập chức năng
Kiểm tra quyền của tài khoản với quản trị viên

## 11. LIÊN HỆ HỖ TRỢ

Khi gặp vấn đề kỹ thuật, vui lòng liên hệ:
- Email: <EMAIL>
- Hotline: 1900-xxxx
- Cung cấp thông tin: Tài khoản, thời gian xảy ra lỗi, mô tả chi tiết

---
*Tài liệu này được cập nhật lần cuối: [Ngày tháng năm]*
*Phiên bản hệ thống: v1.0*
